

:root {
  /* 基础颜色 */
  --white: #fff;
  --gray-100: #f8f9fa;
  --gray-200: rgb(244, 244, 244);
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --black: #000;
  /* 常见颜色 */
  --blue: #409eff;
  --red: #f56c6c;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  /* 主题色 */
  --theme-color: #409eff;
  /* 提示色 */
  --primary: var(--blue);
  --secondary: var(--gray-600);
  --success: var(--green);
  --info: var(--cyan);
  --warning: var(--yellow);
  --danger: var(--red);
  --light: var(--gray-100);
  --dark: var(--gray-800);
  /* 字体 */
  --font-size-base: 16px;
  --font-size-sm: 14px;
  --font-size-xs: 12px;
  --font-size-lg: 16px;
  --font-size-ex: 20px;
  --font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;
  /* 圆角 */
  --border-radius-xs: 2px;
  --border-radius-sm: 4px;
  --border-radius-base: 5px;
  --border-radius: 5px;
  --border-radius-bg: 10px;
  --border-radius-lg: 15px;
  --border-radius-round: 50%;
  /* 阴影 */
  --box-shadow-sm: 0 0 4px rgba(0, 0, 0, 0.1);
  --box-shadow-base: 0 0 8px rgba(0, 0, 0, 0.1);
  --box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
  --box-shadow-bg: 0 0 16px rgba(0, 0, 0, 0.1);
  /* zIndex */
  --zIndex-fieldset: 2;
  --zIndex-share-header: 1994;
  --zIndex-header: 1995;
  --zIndex-tabs: 1995;
  --zIndex-panel: 1996;
  --zIndex-editor: 1994;
  --zIndex-dropdown: 1900;
  --zIndex-request-info-wrap: 1995;
  --zIndex-history-dropdown: 1901;
  --zIndex-drag-bar: 1996;
  --zIndex-contextmenu: 1996;
  --zIndex-loading-text: 2222;
  --zIndex-copy: 2599;
  --zIndex-language: 2600;
  --el-transition-duration: 0;
  /* 业务变量 */
  --apiflow-header-height: 35px;
  --apiflow-doc-nav-height: 40px;
  --apiflow-banner-tool-height: 150px;
  --apiflow-apidoc-operation-height: 100px;
  --apiflow-apidoc-request-view-height: 170px;
}

* {
  box-sizing: border-box;
}

html {
  font-size: 14px;
  height: 100%;
  font-family: var(--font-family);
}

body {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  overflow-x: hidden;
  color: #495057;
}

button,
input,
div,
span,
textarea {
  outline: none;
  padding: 0;
}

pre.pre {
  margin: 0;
  overflow-x: auto;
  padding: 7px 10px;
  border: 1px solid #d1d5da;
  border-radius: 4px;
  background-color: #f0f0f0;
  white-space: pre-wrap;
  display: block;
  color: #212529;
  font-size: 87.5%;
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

body .el-popper {
  font-size: 14px;
}

body .el-dropdown-menu {
  max-height: 300px;
  overflow-y: auto;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px 0px;
}

body .el-dropdown-menu .popper__arrow {
  display: none;
}

body .el-dropdown-menu__item:focus,
body .el-dropdown-menu__item:not(.is-disabled):hover {
  background: #f4f4f4;
  color: #409EFF;
}

body .el-table .cell {
  line-height: 24px;
}

body .el-image-viewer__close {
  background-color: #fff;
  color: #f56c6c;
}

body .el-form--label-top .el-form-item__label {
  padding: 0;
}

body .el-dialog__body {
  padding-top: 0;
  padding-bottom: 10px;
}

body .el-dialog__headerbtn {
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  top: 10px;
  right: 10px;
}

body .el-dialog__headerbtn:hover {
  background-color: #dee2e6;
}

body .el-dialog__headerbtn .el-dialog__close {
  font-size: 20px;
}

body .el-dialog__headerbtn .el-dialog__close:hover {
  color: #f56c6c;
}

body .el-input.is-disabled .el-input__inner {
  box-shadow: none;
}

body .el-input.is-disabled .el-input__inner {
  border-bottom: none;
}

body .el-menu--horizontal {
  border-bottom: none;
}

.primary {
  color: #409EFF;
}

.secondary {
  color: #6c757d;
}

.success {
  color: #28a745;
}

.info {
  color: #17a2b8;
}

.warning {
  color: #ffc107;
}

.danger {
  color: #f56c6c;
}

.light {
  color: #f8f9fa;
}

.dark {
  color: #343a40;
}

.blue {
  color: #409EFF;
}

.indigo {
  color: #6610f2;
}

.purple {
  color: #6f42c1;
}

.pink {
  color: #f9a2c4;
}

.red {
  color: #f56c6c;
}

.orange {
  color: #fd7e14;
}

.yellow {
  color: #ffc107;
}

.green {
  color: #28a745;
}

.teal {
  color: #20c997;
}

.cyan {
  color: #17a2b8;
}

.black {
  color: #000;
}

.white {
  color: #fff;
}

.theme-color {
  color: #409EFF;
}

.gray-100 {
  color: #f8f9fa;
}

.gray-200 {
  color: #f4f4f4;
}

.gray-300 {
  color: #dee2e6;
}

.gray-400 {
  color: #ced4da;
}

.gray-500 {
  color: #adb5bd;
}

.gray-600 {
  color: #6c757d;
}

.gray-700 {
  color: #495057;
}

.gray-800 {
  color: #343a40;
}

.gray-900 {
  color: #212529;
}

.bg-primary {
  background-color: #409EFF;
}

.bg-secondary {
  background-color: #6c757d;
}

.bg-success {
  background-color: #28a745;
}

.bg-info {
  background-color: #17a2b8;
}

.bg-warning {
  background-color: #ffc107;
}

.bg-danger {
  background-color: #f56c6c;
}

.bg-light {
  background-color: #f8f9fa;
}

.bg-dark {
  background-color: #343a40;
}

.bg-blue {
  background-color: #409EFF;
}

.bg-indigo {
  background-color: #6610f2;
}

.bg-purple {
  background-color: #6f42c1;
}

.bg-pink {
  background-color: #f9a2c4;
}

.bg-red {
  background-color: #f56c6c;
}

.bg-orange {
  background-color: #fd7e14;
}

.bg-yellow {
  background-color: #ffc107;
}

.bg-green {
  background-color: #28a745;
}

.bg-teal {
  background-color: #20c997;
}

.bg-cyan {
  background-color: #17a2b8;
}

.bg-black {
  background-color: #000;
}

.bg-white {
  background-color: #fff;
}

.bg-theme-color {
  background-color: #409EFF;
}

.bg-gray-100 {
  background-color: #f8f9fa;
}

.bg-gray-200 {
  background-color: #f4f4f4;
}

.bg-gray-300 {
  background-color: #dee2e6;
}

.bg-gray-400 {
  background-color: #ced4da;
}

.bg-gray-500 {
  background-color: #adb5bd;
}

.bg-gray-600 {
  background-color: #6c757d;
}

.bg-gray-700 {
  background-color: #495057;
}

.bg-gray-800 {
  background-color: #343a40;
}

.bg-gray-900 {
  background-color: #212529;
}

.fl {
  float: left !important;
}

.fr {
  float: right !important;
}

.clearfix::after {
  display: block;
  content: "";
  clear: both;
}
.text-underline {
  text-decoration: underline;
}
.text-ellipsis {
  white-space: nowrap;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-ellipsis2 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis3 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-ellipsis4 {
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.overflow-hidden {
  overflow: hidden;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-nowrap {
  white-space: nowrap;
}

.text-normal {
  font-weight: 400;
}

.text-light {
  font-weight: 300;
}

.text-bold {
  font-weight: 700;
}

.p-absolute {
  position: absolute;
}

.p-relative {
  position: relative;
}

.p-static {
  position: static;
}

.p-sticky {
  position: sticky;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.border-top-primary {
  border-top: 1px solid #409EFF;
}

.border-top-secondary {
  border-top: 1px solid #6c757d;
}

.border-top-success {
  border-top: 1px solid #28a745;
}

.border-top-info {
  border-top: 1px solid #17a2b8;
}

.border-top-warning {
  border-top: 1px solid #ffc107;
}

.border-top-danger {
  border-top: 1px solid #f56c6c;
}

.border-top-light {
  border-top: 1px solid #f8f9fa;
}

.border-top-dark {
  border-top: 1px solid #343a40;
}

.border-top-blue {
  border-top: 1px solid #409EFF;
}

.border-top-indigo {
  border-top: 1px solid #6610f2;
}

.border-top-purple {
  border-top: 1px solid #6f42c1;
}

.border-top-pink {
  border-top: 1px solid #f9a2c4;
}

.border-top-red {
  border-top: 1px solid #f56c6c;
}

.border-top-orange {
  border-top: 1px solid #fd7e14;
}

.border-top-yellow {
  border-top: 1px solid #ffc107;
}

.border-top-green {
  border-top: 1px solid #28a745;
}

.border-top-teal {
  border-top: 1px solid #20c997;
}

.border-top-cyan {
  border-top: 1px solid #17a2b8;
}

.border-top-black {
  border-top: 1px solid #000;
}

.border-top-white {
  border-top: 1px solid #fff;
}

.border-top-theme-color {
  border-top: 1px solid #409EFF;
}

.border-top-gray-100 {
  border-top: 1px solid #f8f9fa;
}

.border-top-gray-200 {
  border-top: 1px solid #f4f4f4;
}

.border-top-gray-300 {
  border-top: 1px solid #dee2e6;
}

.border-top-gray-400 {
  border-top: 1px solid #ced4da;
}

.border-top-gray-500 {
  border-top: 1px solid #adb5bd;
}

.border-top-gray-600 {
  border-top: 1px solid #6c757d;
}

.border-top-gray-700 {
  border-top: 1px solid #495057;
}

.border-top-gray-800 {
  border-top: 1px solid #343a40;
}

.border-top-gray-900 {
  border-top: 1px solid #212529;
}

.border-bottom-primary {
  border-bottom: 1px solid #409EFF;
}

.border-bottom-secondary {
  border-bottom: 1px solid #6c757d;
}

.border-bottom-success {
  border-bottom: 1px solid #28a745;
}

.border-bottom-info {
  border-bottom: 1px solid #17a2b8;
}

.border-bottom-warning {
  border-bottom: 1px solid #ffc107;
}

.border-bottom-danger {
  border-bottom: 1px solid #f56c6c;
}

.border-bottom-light {
  border-bottom: 1px solid #f8f9fa;
}

.border-bottom-dark {
  border-bottom: 1px solid #343a40;
}

.border-bottom-blue {
  border-bottom: 1px solid #409EFF;
}

.border-bottom-indigo {
  border-bottom: 1px solid #6610f2;
}

.border-bottom-purple {
  border-bottom: 1px solid #6f42c1;
}

.border-bottom-pink {
  border-bottom: 1px solid #f9a2c4;
}

.border-bottom-red {
  border-bottom: 1px solid #f56c6c;
}

.border-bottom-orange {
  border-bottom: 1px solid #fd7e14;
}

.border-bottom-yellow {
  border-bottom: 1px solid #ffc107;
}

.border-bottom-green {
  border-bottom: 1px solid #28a745;
}

.border-bottom-teal {
  border-bottom: 1px solid #20c997;
}

.border-bottom-cyan {
  border-bottom: 1px solid #17a2b8;
}

.border-bottom-black {
  border-bottom: 1px solid #000;
}

.border-bottom-white {
  border-bottom: 1px solid #fff;
}

.border-bottom-theme-color {
  border-bottom: 1px solid #409EFF;
}

.border-bottom-gray-100 {
  border-bottom: 1px solid #f8f9fa;
}

.border-bottom-gray-200 {
  border-bottom: 1px solid #f4f4f4;
}

.border-bottom-gray-300 {
  border-bottom: 1px solid #dee2e6;
}

.border-bottom-gray-400 {
  border-bottom: 1px solid #ced4da;
}

.border-bottom-gray-500 {
  border-bottom: 1px solid #adb5bd;
}

.border-bottom-gray-600 {
  border-bottom: 1px solid #6c757d;
}

.border-bottom-gray-700 {
  border-bottom: 1px solid #495057;
}

.border-bottom-gray-800 {
  border-bottom: 1px solid #343a40;
}

.border-bottom-gray-900 {
  border-bottom: 1px solid #212529;
}

.border-left-primary {
  border-left: 1px solid #409EFF;
}

.border-left-secondary {
  border-left: 1px solid #6c757d;
}

.border-left-success {
  border-left: 1px solid #28a745;
}

.border-left-info {
  border-left: 1px solid #17a2b8;
}

.border-left-warning {
  border-left: 1px solid #ffc107;
}

.border-left-danger {
  border-left: 1px solid #f56c6c;
}

.border-left-light {
  border-left: 1px solid #f8f9fa;
}

.border-left-dark {
  border-left: 1px solid #343a40;
}

.border-left-blue {
  border-left: 1px solid #409EFF;
}

.border-left-indigo {
  border-left: 1px solid #6610f2;
}

.border-left-purple {
  border-left: 1px solid #6f42c1;
}

.border-left-pink {
  border-left: 1px solid #f9a2c4;
}

.border-left-red {
  border-left: 1px solid #f56c6c;
}

.border-left-orange {
  border-left: 1px solid #fd7e14;
}

.border-left-yellow {
  border-left: 1px solid #ffc107;
}

.border-left-green {
  border-left: 1px solid #28a745;
}

.border-left-teal {
  border-left: 1px solid #20c997;
}

.border-left-cyan {
  border-left: 1px solid #17a2b8;
}

.border-left-black {
  border-left: 1px solid #000;
}

.border-left-white {
  border-left: 1px solid #fff;
}

.border-left-theme-color {
  border-left: 1px solid #409EFF;
}

.border-left-gray-100 {
  border-left: 1px solid #f8f9fa;
}

.border-left-gray-200 {
  border-left: 1px solid #f4f4f4;
}

.border-left-gray-300 {
  border-left: 1px solid #dee2e6;
}

.border-left-gray-400 {
  border-left: 1px solid #ced4da;
}

.border-left-gray-500 {
  border-left: 1px solid #adb5bd;
}

.border-left-gray-600 {
  border-left: 1px solid #6c757d;
}

.border-left-gray-700 {
  border-left: 1px solid #495057;
}

.border-left-gray-800 {
  border-left: 1px solid #343a40;
}

.border-left-gray-900 {
  border-left: 1px solid #212529;
}

.border-right-primary {
  border-right: 1px solid #409EFF;
}

.border-right-secondary {
  border-right: 1px solid #6c757d;
}

.border-right-success {
  border-right: 1px solid #28a745;
}

.border-right-info {
  border-right: 1px solid #17a2b8;
}

.border-right-warning {
  border-right: 1px solid #ffc107;
}

.border-right-danger {
  border-right: 1px solid #f56c6c;
}

.border-right-light {
  border-right: 1px solid #f8f9fa;
}

.border-right-dark {
  border-right: 1px solid #343a40;
}

.border-right-blue {
  border-right: 1px solid #409EFF;
}

.border-right-indigo {
  border-right: 1px solid #6610f2;
}

.border-right-purple {
  border-right: 1px solid #6f42c1;
}

.border-right-pink {
  border-right: 1px solid #f9a2c4;
}

.border-right-red {
  border-right: 1px solid #f56c6c;
}

.border-right-orange {
  border-right: 1px solid #fd7e14;
}

.border-right-yellow {
  border-right: 1px solid #ffc107;
}

.border-right-green {
  border-right: 1px solid #28a745;
}

.border-right-teal {
  border-right: 1px solid #20c997;
}

.border-right-cyan {
  border-right: 1px solid #17a2b8;
}

.border-right-black {
  border-right: 1px solid #000;
}

.border-right-white {
  border-right: 1px solid #fff;
}

.border-right-theme-color {
  border-right: 1px solid #409EFF;
}

.border-right-gray-100 {
  border-right: 1px solid #f8f9fa;
}

.border-right-gray-200 {
  border-right: 1px solid #f4f4f4;
}

.border-right-gray-300 {
  border-right: 1px solid #dee2e6;
}

.border-right-gray-400 {
  border-right: 1px solid #ced4da;
}

.border-right-gray-500 {
  border-right: 1px solid #adb5bd;
}

.border-right-gray-600 {
  border-right: 1px solid #6c757d;
}

.border-right-gray-700 {
  border-right: 1px solid #495057;
}

.border-right-gray-800 {
  border-right: 1px solid #343a40;
}

.border-right-gray-900 {
  border-right: 1px solid #212529;
}

.border-primary {
  border: 1px solid #409EFF;
}

.border-secondary {
  border: 1px solid #6c757d;
}

.border-success {
  border: 1px solid #28a745;
}

.border-info {
  border: 1px solid #17a2b8;
}

.border-warning {
  border: 1px solid #ffc107;
}

.border-danger {
  border: 1px solid #f56c6c;
}

.border-light {
  border: 1px solid #f8f9fa;
}

.border-dark {
  border: 1px solid #343a40;
}

.border-blue {
  border: 1px solid #409EFF;
}

.border-indigo {
  border: 1px solid #6610f2;
}

.border-purple {
  border: 1px solid #6f42c1;
}

.border-pink {
  border: 1px solid #f9a2c4;
}

.border-red {
  border: 1px solid #f56c6c;
}

.border-orange {
  border: 1px solid #fd7e14;
}

.border-yellow {
  border: 1px solid #ffc107;
}

.border-green {
  border: 1px solid #28a745;
}

.border-teal {
  border: 1px solid #20c997;
}

.border-cyan {
  border: 1px solid #17a2b8;
}

.border-black {
  border: 1px solid #000;
}

.border-white {
  border: 1px solid #fff;
}

.border-theme-color {
  border: 1px solid #409EFF;
}

.border-gray-100 {
  border: 1px solid #f8f9fa;
}

.border-gray-200 {
  border: 1px solid #f4f4f4;
}

.border-gray-300 {
  border: 1px solid #dee2e6;
}

.border-gray-400 {
  border: 1px solid #ced4da;
}

.border-gray-500 {
  border: 1px solid #adb5bd;
}

.border-gray-600 {
  border: 1px solid #6c757d;
}

.border-gray-700 {
  border: 1px solid #495057;
}

.border-gray-800 {
  border: 1px solid #343a40;
}

.border-gray-900 {
  border: 1px solid #212529;
}

.cursor-default {
  cursor: default;
}

.cursor-auto {
  cursor: auto;
}

.cursor-ew-resize {
  cursor: ew-resize;
}

.cursor-crosshair {
  cursor: crosshair;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-move {
  cursor: move;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-zoom-in {
  cursor: zoom-in;
}

.cursor-wait {
  cursor: wait;
}

.cursor-grab {
  cursor: grab;
}

.cursor-help {
  cursor: help;
}

.box-shadow-sm {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 4px 0px;
}

.box-shadow-base {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 8px 0px;
}

.box-shadow {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 8px 0px;
}

.box-shadow-bg {
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 16px 0px;
}

.radius-xs {
  border-radius: 2px;
}

.radius-sm {
  border-radius: 4px;
}

.radius-base {
  border-radius: 5px;
}

.radius {
  border-radius: 5px;
}

.radius-bg {
  border-radius: 10px;
}

.radius-lg {
  border-radius: 15px;
}

.radius-round {
  border-radius: 50%;
}

.m-auto {
  margin: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-auto {
  margin-right: auto;
}

.no-padding {
  padding: 0 !important;
}

.no-margin {
  margin: 0 !important;
}

.mt-0 {
  margin-top: 0px;
}

.pt-0 {
  padding-top: 0px;
}

.ml-0 {
  margin-left: 0px;
}

.pl-0 {
  padding-left: 0px;
}

.mr-0 {
  margin-right: 0px;
}

.pr-0 {
  padding-right: 0px;
}

.mb-0 {
  margin-bottom: 0px;
}

.pb-0 {
  padding-bottom: 0px;
}

.mt-1 {
  margin-top: 5px;
}

.pt-1 {
  padding-top: 5px;
}

.ml-1 {
  margin-left: 5px;
}

.pl-1 {
  padding-left: 5px;
}

.mr-1 {
  margin-right: 5px;
}

.pr-1 {
  padding-right: 5px;
}

.mb-1 {
  margin-bottom: 5px;
}

.pb-1 {
  padding-bottom: 5px;
}

.mt-2 {
  margin-top: 10px;
}

.pt-2 {
  padding-top: 10px;
}

.ml-2 {
  margin-left: 10px;
}

.pl-2 {
  padding-left: 10px;
}

.mr-2 {
  margin-right: 10px;
}

.pr-2 {
  padding-right: 10px;
}

.mb-2 {
  margin-bottom: 10px;
}

.pb-2 {
  padding-bottom: 10px;
}

.mt-3 {
  margin-top: 15px;
}

.pt-3 {
  padding-top: 15px;
}

.ml-3 {
  margin-left: 15px;
}

.pl-3 {
  padding-left: 15px;
}

.mr-3 {
  margin-right: 15px;
}

.pr-3 {
  padding-right: 15px;
}

.mb-3 {
  margin-bottom: 15px;
}

.pb-3 {
  padding-bottom: 15px;
}

.mt-4 {
  margin-top: 20px;
}

.pt-4 {
  padding-top: 20px;
}

.ml-4 {
  margin-left: 20px;
}

.pl-4 {
  padding-left: 20px;
}

.mr-4 {
  margin-right: 20px;
}

.pr-4 {
  padding-right: 20px;
}

.mb-4 {
  margin-bottom: 20px;
}

.pb-4 {
  padding-bottom: 20px;
}

.mt-5 {
  margin-top: 25px;
}

.pt-5 {
  padding-top: 25px;
}

.ml-5 {
  margin-left: 25px;
}

.pl-5 {
  padding-left: 25px;
}

.mr-5 {
  margin-right: 25px;
}

.pr-5 {
  padding-right: 25px;
}

.mb-5 {
  margin-bottom: 25px;
}

.pb-5 {
  padding-bottom: 25px;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.mx-1 {
  margin-left: 5px;
  margin-right: 5px;
}

.my-1 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.px-1 {
  padding-left: 5px;
  padding-right: 5px;
}

.py-1 {
  padding-top: 5px;
  padding-bottom: 5px;
}

.mx-2 {
  margin-left: 10px;
  margin-right: 10px;
}

.my-2 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.px-2 {
  padding-left: 10px;
  padding-right: 10px;
}

.py-2 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.mx-3 {
  margin-left: 15px;
  margin-right: 15px;
}

.my-3 {
  margin-top: 15px;
  margin-bottom: 15px;
}

.px-3 {
  padding-left: 15px;
  padding-right: 15px;
}

.py-3 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.mx-4 {
  margin-left: 20px;
  margin-right: 20px;
}

.my-4 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.px-4 {
  padding-left: 20px;
  padding-right: 20px;
}

.py-4 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.mx-5 {
  margin-left: 25px;
  margin-right: 25px;
}

.my-5 {
  margin-top: 25px;
  margin-bottom: 25px;
}

.px-5 {
  padding-left: 25px;
  padding-right: 25px;
}

.py-5 {
  padding-top: 25px;
  padding-bottom: 25px;
}

.f-base {
  font-size: 16px;
}

.f-mid {
  font-size: 15px;
}

.f-sm {
  font-size: 14px;
}

.f-xs {
  font-size: 12px;
}

.f-bg {
  font-size: 20px;
}

.f-lg {
  font-size: 21.6px;
}

.f-ex {
  font-size: 32px;
}

.f-0 {
  font-size: 0px;
}

.f-10 {
  font-size: 10px;
}

.f-20 {
  font-size: 20px;
}

.f-30 {
  font-size: 30px;
}

.f-40 {
  font-size: 40px;
}

.f-50 {
  font-size: 50px;
}

.f-60 {
  font-size: 60px;
}

.f-70 {
  font-size: 70px;
}

.f-80 {
  font-size: 80px;
}

.f-90 {
  font-size: 90px;
}

.f-100 {
  font-size: 100px;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

.d-none {
  display: none;
}

.d-flex {
  display: flex;
}

.d-flex.j-start {
  justify-content: flex-start;
}

.d-flex.j-center {
  justify-content: center;
}

.d-flex.j-end {
  justify-content: flex-end;
}

.d-flex.around {
  justify-content: space-around;
}

.d-flex.between {
  justify-content: space-between;
}

.d-flex.evenly {
  justify-content: space-evenly;
}

.d-flex.j-around {
  justify-content: space-around;
}

.d-flex.j-between {
  justify-content: space-between;
}

.d-flex.j-evenly {
  justify-content: space-evenly;
}

.d-flex.justify-start {
  justify-content: flex-start;
}

.d-flex.justify-center {
  justify-content: center;
}

.d-flex.justify-end {
  justify-content: flex-end;
}

.d-flex.justify-around {
  justify-content: space-around;
}

.d-flex.justify-between {
  justify-content: space-between;
}

.d-flex.justify-evenly {
  justify-content: space-evenly;
}

.d-flex.a-start {
  align-items: flex-start;
}

.d-flex.a-center {
  align-items: center;
}

.d-flex.a-end {
  align-items: flex-end;
}

.d-flex.a-around {
  align-items: space-around;
}

.d-flex.a-between {
  align-items: space-between;
}

.d-flex.flex-center {
  align-items: center;
  justify-content: center;
}

.d-flex.center {
  align-items: center;
  justify-content: center;
}

.d-flex.flex-row {
  flex-direction: row;
}

.d-flex.flex-column {
  flex-direction: column;
}

.d-flex.flex-wrap {
  flex-wrap: wrap;
}

.d-inline-flex {
  display: inline-flex;
}

.d-inline-flex.j-start {
  justify-content: flex-start;
}

.d-inline-flex.j-center {
  justify-content: center;
}

.d-inline-flex.j-end {
  justify-content: flex-end;
}

.d-inline-flex.around {
  justify-content: space-around;
}

.d-inline-flex.between {
  justify-content: space-between;
}

.d-inline-flex.evenly {
  justify-content: space-evenly;
}

.d-inline-flex.j-around {
  justify-content: space-around;
}

.d-inline-flex.j-between {
  justify-content: space-between;
}

.d-inline-flex.j-evenly {
  justify-content: space-evenly;
}

.d-inline-flex.justify-start {
  justify-content: flex-start;
}

.d-inline-flex.justify-center {
  justify-content: center;
}

.d-inline-flex.justify-end {
  justify-content: flex-end;
}

.d-inline-flex.justify-around {
  justify-content: space-around;
}

.d-inline-flex.justify-between {
  justify-content: space-between;
}

.d-inline-flex.justify-evenly {
  justify-content: space-evenly;
}

.d-inline-flex.a-start {
  align-items: flex-start;
}

.d-inline-flex.a-center {
  align-items: center;
}

.d-inline-flex.a-end {
  align-items: flex-end;
}

.d-inline-flex.a-around {
  align-items: space-around;
}

.d-inline-flex.a-between {
  align-items: space-between;
}

.d-inline-flex.flex-center {
  align-items: center;
  justify-content: center;
}

.d-inline-flex.center {
  align-items: center;
  justify-content: center;
}

.d-inline-flex.flex-row {
  flex-direction: row;
}

.d-inline-flex.flex-column {
  flex-direction: column;
}

.d-inline-flex.flex-wrap {
  flex-wrap: wrap;
}

.flex1 {
  flex: 1;
}

.flex0 {
  flex: 0 0 auto;
}

.w-1 {
  width: 1%;
}

.h-1 {
  height: 1%;
}

.w-10px {
  width: 10px;
}

.h-10px {
  height: 10px;
}

.vw-1 {
  width: 1vw;
}

.vh-1 {
  height: 1vh;
}

.w-2 {
  width: 2%;
}

.h-2 {
  height: 2%;
}

.w-20px {
  width: 20px;
}

.h-20px {
  height: 20px;
}

.vw-2 {
  width: 2vw;
}

.vh-2 {
  height: 2vh;
}

.w-3 {
  width: 3%;
}

.h-3 {
  height: 3%;
}

.w-30px {
  width: 30px;
}

.h-30px {
  height: 30px;
}

.vw-3 {
  width: 3vw;
}

.vh-3 {
  height: 3vh;
}

.w-4 {
  width: 4%;
}

.h-4 {
  height: 4%;
}

.w-40px {
  width: 40px;
}

.h-40px {
  height: 40px;
}

.vw-4 {
  width: 4vw;
}

.vh-4 {
  height: 4vh;
}

.w-5 {
  width: 5%;
}

.h-5 {
  height: 5%;
}

.w-50px {
  width: 50px;
}

.h-50px {
  height: 50px;
}

.vw-5 {
  width: 5vw;
}

.vh-5 {
  height: 5vh;
}

.w-6 {
  width: 6%;
}

.h-6 {
  height: 6%;
}

.w-60px {
  width: 60px;
}

.h-60px {
  height: 60px;
}

.vw-6 {
  width: 6vw;
}

.vh-6 {
  height: 6vh;
}

.w-7 {
  width: 7%;
}

.h-7 {
  height: 7%;
}

.w-70px {
  width: 70px;
}

.h-70px {
  height: 70px;
}

.vw-7 {
  width: 7vw;
}

.vh-7 {
  height: 7vh;
}

.w-8 {
  width: 8%;
}

.h-8 {
  height: 8%;
}

.w-80px {
  width: 80px;
}

.h-80px {
  height: 80px;
}

.vw-8 {
  width: 8vw;
}

.vh-8 {
  height: 8vh;
}

.w-9 {
  width: 9%;
}

.h-9 {
  height: 9%;
}

.w-90px {
  width: 90px;
}

.h-90px {
  height: 90px;
}

.vw-9 {
  width: 9vw;
}

.vh-9 {
  height: 9vh;
}

.w-10 {
  width: 10%;
}

.h-10 {
  height: 10%;
}

.w-100px {
  width: 100px;
}

.h-100px {
  height: 100px;
}

.vw-10 {
  width: 10vw;
}

.vh-10 {
  height: 10vh;
}

.w-11 {
  width: 11%;
}

.h-11 {
  height: 11%;
}

.w-110px {
  width: 110px;
}

.h-110px {
  height: 110px;
}

.vw-11 {
  width: 11vw;
}

.vh-11 {
  height: 11vh;
}

.w-12 {
  width: 12%;
}

.h-12 {
  height: 12%;
}

.w-120px {
  width: 120px;
}

.h-120px {
  height: 120px;
}

.vw-12 {
  width: 12vw;
}

.vh-12 {
  height: 12vh;
}

.w-13 {
  width: 13%;
}

.h-13 {
  height: 13%;
}

.w-130px {
  width: 130px;
}

.h-130px {
  height: 130px;
}

.vw-13 {
  width: 13vw;
}

.vh-13 {
  height: 13vh;
}

.w-14 {
  width: 14%;
}

.h-14 {
  height: 14%;
}

.w-140px {
  width: 140px;
}

.h-140px {
  height: 140px;
}

.vw-14 {
  width: 14vw;
}

.vh-14 {
  height: 14vh;
}

.w-15 {
  width: 15%;
}

.h-15 {
  height: 15%;
}

.w-150px {
  width: 150px;
}

.h-150px {
  height: 150px;
}

.vw-15 {
  width: 15vw;
}

.vh-15 {
  height: 15vh;
}

.w-16 {
  width: 16%;
}

.h-16 {
  height: 16%;
}

.w-160px {
  width: 160px;
}

.h-160px {
  height: 160px;
}

.vw-16 {
  width: 16vw;
}

.vh-16 {
  height: 16vh;
}

.w-17 {
  width: 17%;
}

.h-17 {
  height: 17%;
}

.w-170px {
  width: 170px;
}

.h-170px {
  height: 170px;
}

.vw-17 {
  width: 17vw;
}

.vh-17 {
  height: 17vh;
}

.w-18 {
  width: 18%;
}

.h-18 {
  height: 18%;
}

.w-180px {
  width: 180px;
}

.h-180px {
  height: 180px;
}

.vw-18 {
  width: 18vw;
}

.vh-18 {
  height: 18vh;
}

.w-19 {
  width: 19%;
}

.h-19 {
  height: 19%;
}

.w-190px {
  width: 190px;
}

.h-190px {
  height: 190px;
}

.vw-19 {
  width: 19vw;
}

.vh-19 {
  height: 19vh;
}

.w-20 {
  width: 20%;
}

.h-20 {
  height: 20%;
}

.w-200px {
  width: 200px;
}

.h-200px {
  height: 200px;
}

.vw-20 {
  width: 20vw;
}

.vh-20 {
  height: 20vh;
}

.w-21 {
  width: 21%;
}

.h-21 {
  height: 21%;
}

.w-210px {
  width: 210px;
}

.h-210px {
  height: 210px;
}

.vw-21 {
  width: 21vw;
}

.vh-21 {
  height: 21vh;
}

.w-22 {
  width: 22%;
}

.h-22 {
  height: 22%;
}

.w-220px {
  width: 220px;
}

.h-220px {
  height: 220px;
}

.vw-22 {
  width: 22vw;
}

.vh-22 {
  height: 22vh;
}

.w-23 {
  width: 23%;
}

.h-23 {
  height: 23%;
}

.w-230px {
  width: 230px;
}

.h-230px {
  height: 230px;
}

.vw-23 {
  width: 23vw;
}

.vh-23 {
  height: 23vh;
}

.w-24 {
  width: 24%;
}

.h-24 {
  height: 24%;
}

.w-240px {
  width: 240px;
}

.h-240px {
  height: 240px;
}

.vw-24 {
  width: 24vw;
}

.vh-24 {
  height: 24vh;
}

.w-25 {
  width: 25%;
}

.h-25 {
  height: 25%;
}

.w-250px {
  width: 250px;
}

.h-250px {
  height: 250px;
}

.vw-25 {
  width: 25vw;
}

.vh-25 {
  height: 25vh;
}

.w-26 {
  width: 26%;
}

.h-26 {
  height: 26%;
}

.w-260px {
  width: 260px;
}

.h-260px {
  height: 260px;
}

.vw-26 {
  width: 26vw;
}

.vh-26 {
  height: 26vh;
}

.w-27 {
  width: 27%;
}

.h-27 {
  height: 27%;
}

.w-270px {
  width: 270px;
}

.h-270px {
  height: 270px;
}

.vw-27 {
  width: 27vw;
}

.vh-27 {
  height: 27vh;
}

.w-28 {
  width: 28%;
}

.h-28 {
  height: 28%;
}

.w-280px {
  width: 280px;
}

.h-280px {
  height: 280px;
}

.vw-28 {
  width: 28vw;
}

.vh-28 {
  height: 28vh;
}

.w-29 {
  width: 29%;
}

.h-29 {
  height: 29%;
}

.w-290px {
  width: 290px;
}

.h-290px {
  height: 290px;
}

.vw-29 {
  width: 29vw;
}

.vh-29 {
  height: 29vh;
}

.w-30 {
  width: 30%;
}

.h-30 {
  height: 30%;
}

.w-300px {
  width: 300px;
}

.h-300px {
  height: 300px;
}

.vw-30 {
  width: 30vw;
}

.vh-30 {
  height: 30vh;
}

.w-31 {
  width: 31%;
}

.h-31 {
  height: 31%;
}

.w-310px {
  width: 310px;
}

.h-310px {
  height: 310px;
}

.vw-31 {
  width: 31vw;
}

.vh-31 {
  height: 31vh;
}

.w-32 {
  width: 32%;
}

.h-32 {
  height: 32%;
}

.w-320px {
  width: 320px;
}

.h-320px {
  height: 320px;
}

.vw-32 {
  width: 32vw;
}

.vh-32 {
  height: 32vh;
}

.w-33 {
  width: 33%;
}

.h-33 {
  height: 33%;
}

.w-330px {
  width: 330px;
}

.h-330px {
  height: 330px;
}

.vw-33 {
  width: 33vw;
}

.vh-33 {
  height: 33vh;
}

.w-34 {
  width: 34%;
}

.h-34 {
  height: 34%;
}

.w-340px {
  width: 340px;
}

.h-340px {
  height: 340px;
}

.vw-34 {
  width: 34vw;
}

.vh-34 {
  height: 34vh;
}

.w-35 {
  width: 35%;
}

.h-35 {
  height: 35%;
}

.w-350px {
  width: 350px;
}

.h-350px {
  height: 350px;
}

.vw-35 {
  width: 35vw;
}

.vh-35 {
  height: 35vh;
}

.w-36 {
  width: 36%;
}

.h-36 {
  height: 36%;
}

.w-360px {
  width: 360px;
}

.h-360px {
  height: 360px;
}

.vw-36 {
  width: 36vw;
}

.vh-36 {
  height: 36vh;
}

.w-37 {
  width: 37%;
}

.h-37 {
  height: 37%;
}

.w-370px {
  width: 370px;
}

.h-370px {
  height: 370px;
}

.vw-37 {
  width: 37vw;
}

.vh-37 {
  height: 37vh;
}

.w-38 {
  width: 38%;
}

.h-38 {
  height: 38%;
}

.w-380px {
  width: 380px;
}

.h-380px {
  height: 380px;
}

.vw-38 {
  width: 38vw;
}

.vh-38 {
  height: 38vh;
}

.w-39 {
  width: 39%;
}

.h-39 {
  height: 39%;
}

.w-390px {
  width: 390px;
}

.h-390px {
  height: 390px;
}

.vw-39 {
  width: 39vw;
}

.vh-39 {
  height: 39vh;
}

.w-40 {
  width: 40%;
}

.h-40 {
  height: 40%;
}

.w-400px {
  width: 400px;
}

.h-400px {
  height: 400px;
}

.vw-40 {
  width: 40vw;
}

.vh-40 {
  height: 40vh;
}

.w-41 {
  width: 41%;
}

.h-41 {
  height: 41%;
}

.w-410px {
  width: 410px;
}

.h-410px {
  height: 410px;
}

.vw-41 {
  width: 41vw;
}

.vh-41 {
  height: 41vh;
}

.w-42 {
  width: 42%;
}

.h-42 {
  height: 42%;
}

.w-420px {
  width: 420px;
}

.h-420px {
  height: 420px;
}

.vw-42 {
  width: 42vw;
}

.vh-42 {
  height: 42vh;
}

.w-43 {
  width: 43%;
}

.h-43 {
  height: 43%;
}

.w-430px {
  width: 430px;
}

.h-430px {
  height: 430px;
}

.vw-43 {
  width: 43vw;
}

.vh-43 {
  height: 43vh;
}

.w-44 {
  width: 44%;
}

.h-44 {
  height: 44%;
}

.w-440px {
  width: 440px;
}

.h-440px {
  height: 440px;
}

.vw-44 {
  width: 44vw;
}

.vh-44 {
  height: 44vh;
}

.w-45 {
  width: 45%;
}

.h-45 {
  height: 45%;
}

.w-450px {
  width: 450px;
}

.h-450px {
  height: 450px;
}

.vw-45 {
  width: 45vw;
}

.vh-45 {
  height: 45vh;
}

.w-46 {
  width: 46%;
}

.h-46 {
  height: 46%;
}

.w-460px {
  width: 460px;
}

.h-460px {
  height: 460px;
}

.vw-46 {
  width: 46vw;
}

.vh-46 {
  height: 46vh;
}

.w-47 {
  width: 47%;
}

.h-47 {
  height: 47%;
}

.w-470px {
  width: 470px;
}

.h-470px {
  height: 470px;
}

.vw-47 {
  width: 47vw;
}

.vh-47 {
  height: 47vh;
}

.w-48 {
  width: 48%;
}

.h-48 {
  height: 48%;
}

.w-480px {
  width: 480px;
}

.h-480px {
  height: 480px;
}

.vw-48 {
  width: 48vw;
}

.vh-48 {
  height: 48vh;
}

.w-49 {
  width: 49%;
}

.h-49 {
  height: 49%;
}

.w-490px {
  width: 490px;
}

.h-490px {
  height: 490px;
}

.vw-49 {
  width: 49vw;
}

.vh-49 {
  height: 49vh;
}

.w-50 {
  width: 50%;
}

.h-50 {
  height: 50%;
}

.w-500px {
  width: 500px;
}

.h-500px {
  height: 500px;
}

.vw-50 {
  width: 50vw;
}

.vh-50 {
  height: 50vh;
}

.w-51 {
  width: 51%;
}

.h-51 {
  height: 51%;
}

.w-510px {
  width: 510px;
}

.h-510px {
  height: 510px;
}

.vw-51 {
  width: 51vw;
}

.vh-51 {
  height: 51vh;
}

.w-52 {
  width: 52%;
}

.h-52 {
  height: 52%;
}

.w-520px {
  width: 520px;
}

.h-520px {
  height: 520px;
}

.vw-52 {
  width: 52vw;
}

.vh-52 {
  height: 52vh;
}

.w-53 {
  width: 53%;
}

.h-53 {
  height: 53%;
}

.w-530px {
  width: 530px;
}

.h-530px {
  height: 530px;
}

.vw-53 {
  width: 53vw;
}

.vh-53 {
  height: 53vh;
}

.w-54 {
  width: 54%;
}

.h-54 {
  height: 54%;
}

.w-540px {
  width: 540px;
}

.h-540px {
  height: 540px;
}

.vw-54 {
  width: 54vw;
}

.vh-54 {
  height: 54vh;
}

.w-55 {
  width: 55%;
}

.h-55 {
  height: 55%;
}

.w-550px {
  width: 550px;
}

.h-550px {
  height: 550px;
}

.vw-55 {
  width: 55vw;
}

.vh-55 {
  height: 55vh;
}

.w-56 {
  width: 56%;
}

.h-56 {
  height: 56%;
}

.w-560px {
  width: 560px;
}

.h-560px {
  height: 560px;
}

.vw-56 {
  width: 56vw;
}

.vh-56 {
  height: 56vh;
}

.w-57 {
  width: 57%;
}

.h-57 {
  height: 57%;
}

.w-570px {
  width: 570px;
}

.h-570px {
  height: 570px;
}

.vw-57 {
  width: 57vw;
}

.vh-57 {
  height: 57vh;
}

.w-58 {
  width: 58%;
}

.h-58 {
  height: 58%;
}

.w-580px {
  width: 580px;
}

.h-580px {
  height: 580px;
}

.vw-58 {
  width: 58vw;
}

.vh-58 {
  height: 58vh;
}

.w-59 {
  width: 59%;
}

.h-59 {
  height: 59%;
}

.w-590px {
  width: 590px;
}

.h-590px {
  height: 590px;
}

.vw-59 {
  width: 59vw;
}

.vh-59 {
  height: 59vh;
}

.w-60 {
  width: 60%;
}

.h-60 {
  height: 60%;
}

.w-600px {
  width: 600px;
}

.h-600px {
  height: 600px;
}

.vw-60 {
  width: 60vw;
}

.vh-60 {
  height: 60vh;
}

.w-61 {
  width: 61%;
}

.h-61 {
  height: 61%;
}

.w-610px {
  width: 610px;
}

.h-610px {
  height: 610px;
}

.vw-61 {
  width: 61vw;
}

.vh-61 {
  height: 61vh;
}

.w-62 {
  width: 62%;
}

.h-62 {
  height: 62%;
}

.w-620px {
  width: 620px;
}

.h-620px {
  height: 620px;
}

.vw-62 {
  width: 62vw;
}

.vh-62 {
  height: 62vh;
}

.w-63 {
  width: 63%;
}

.h-63 {
  height: 63%;
}

.w-630px {
  width: 630px;
}

.h-630px {
  height: 630px;
}

.vw-63 {
  width: 63vw;
}

.vh-63 {
  height: 63vh;
}

.w-64 {
  width: 64%;
}

.h-64 {
  height: 64%;
}

.w-640px {
  width: 640px;
}

.h-640px {
  height: 640px;
}

.vw-64 {
  width: 64vw;
}

.vh-64 {
  height: 64vh;
}

.w-65 {
  width: 65%;
}

.h-65 {
  height: 65%;
}

.w-650px {
  width: 650px;
}

.h-650px {
  height: 650px;
}

.vw-65 {
  width: 65vw;
}

.vh-65 {
  height: 65vh;
}

.w-66 {
  width: 66%;
}

.h-66 {
  height: 66%;
}

.w-660px {
  width: 660px;
}

.h-660px {
  height: 660px;
}

.vw-66 {
  width: 66vw;
}

.vh-66 {
  height: 66vh;
}

.w-67 {
  width: 67%;
}

.h-67 {
  height: 67%;
}

.w-670px {
  width: 670px;
}

.h-670px {
  height: 670px;
}

.vw-67 {
  width: 67vw;
}

.vh-67 {
  height: 67vh;
}

.w-68 {
  width: 68%;
}

.h-68 {
  height: 68%;
}

.w-680px {
  width: 680px;
}

.h-680px {
  height: 680px;
}

.vw-68 {
  width: 68vw;
}

.vh-68 {
  height: 68vh;
}

.w-69 {
  width: 69%;
}

.h-69 {
  height: 69%;
}

.w-690px {
  width: 690px;
}

.h-690px {
  height: 690px;
}

.vw-69 {
  width: 69vw;
}

.vh-69 {
  height: 69vh;
}

.w-70 {
  width: 70%;
}

.h-70 {
  height: 70%;
}

.w-700px {
  width: 700px;
}

.h-700px {
  height: 700px;
}

.vw-70 {
  width: 70vw;
}

.vh-70 {
  height: 70vh;
}

.w-71 {
  width: 71%;
}

.h-71 {
  height: 71%;
}

.w-710px {
  width: 710px;
}

.h-710px {
  height: 710px;
}

.vw-71 {
  width: 71vw;
}

.vh-71 {
  height: 71vh;
}

.w-72 {
  width: 72%;
}

.h-72 {
  height: 72%;
}

.w-720px {
  width: 720px;
}

.h-720px {
  height: 720px;
}

.vw-72 {
  width: 72vw;
}

.vh-72 {
  height: 72vh;
}

.w-73 {
  width: 73%;
}

.h-73 {
  height: 73%;
}

.w-730px {
  width: 730px;
}

.h-730px {
  height: 730px;
}

.vw-73 {
  width: 73vw;
}

.vh-73 {
  height: 73vh;
}

.w-74 {
  width: 74%;
}

.h-74 {
  height: 74%;
}

.w-740px {
  width: 740px;
}

.h-740px {
  height: 740px;
}

.vw-74 {
  width: 74vw;
}

.vh-74 {
  height: 74vh;
}

.w-75 {
  width: 75%;
}

.h-75 {
  height: 75%;
}

.w-750px {
  width: 750px;
}

.h-750px {
  height: 750px;
}

.vw-75 {
  width: 75vw;
}

.vh-75 {
  height: 75vh;
}

.w-76 {
  width: 76%;
}

.h-76 {
  height: 76%;
}

.w-760px {
  width: 760px;
}

.h-760px {
  height: 760px;
}

.vw-76 {
  width: 76vw;
}

.vh-76 {
  height: 76vh;
}

.w-77 {
  width: 77%;
}

.h-77 {
  height: 77%;
}

.w-770px {
  width: 770px;
}

.h-770px {
  height: 770px;
}

.vw-77 {
  width: 77vw;
}

.vh-77 {
  height: 77vh;
}

.w-78 {
  width: 78%;
}

.h-78 {
  height: 78%;
}

.w-780px {
  width: 780px;
}

.h-780px {
  height: 780px;
}

.vw-78 {
  width: 78vw;
}

.vh-78 {
  height: 78vh;
}

.w-79 {
  width: 79%;
}

.h-79 {
  height: 79%;
}

.w-790px {
  width: 790px;
}

.h-790px {
  height: 790px;
}

.vw-79 {
  width: 79vw;
}

.vh-79 {
  height: 79vh;
}

.w-80 {
  width: 80%;
}

.h-80 {
  height: 80%;
}

.w-800px {
  width: 800px;
}

.h-800px {
  height: 800px;
}

.vw-80 {
  width: 80vw;
}

.vh-80 {
  height: 80vh;
}

.w-81 {
  width: 81%;
}

.h-81 {
  height: 81%;
}

.w-810px {
  width: 810px;
}

.h-810px {
  height: 810px;
}

.vw-81 {
  width: 81vw;
}

.vh-81 {
  height: 81vh;
}

.w-82 {
  width: 82%;
}

.h-82 {
  height: 82%;
}

.w-820px {
  width: 820px;
}

.h-820px {
  height: 820px;
}

.vw-82 {
  width: 82vw;
}

.vh-82 {
  height: 82vh;
}

.w-83 {
  width: 83%;
}

.h-83 {
  height: 83%;
}

.w-830px {
  width: 830px;
}

.h-830px {
  height: 830px;
}

.vw-83 {
  width: 83vw;
}

.vh-83 {
  height: 83vh;
}

.w-84 {
  width: 84%;
}

.h-84 {
  height: 84%;
}

.w-840px {
  width: 840px;
}

.h-840px {
  height: 840px;
}

.vw-84 {
  width: 84vw;
}

.vh-84 {
  height: 84vh;
}

.w-85 {
  width: 85%;
}

.h-85 {
  height: 85%;
}

.w-850px {
  width: 850px;
}

.h-850px {
  height: 850px;
}

.vw-85 {
  width: 85vw;
}

.vh-85 {
  height: 85vh;
}

.w-86 {
  width: 86%;
}

.h-86 {
  height: 86%;
}

.w-860px {
  width: 860px;
}

.h-860px {
  height: 860px;
}

.vw-86 {
  width: 86vw;
}

.vh-86 {
  height: 86vh;
}

.w-87 {
  width: 87%;
}

.h-87 {
  height: 87%;
}

.w-870px {
  width: 870px;
}

.h-870px {
  height: 870px;
}

.vw-87 {
  width: 87vw;
}

.vh-87 {
  height: 87vh;
}

.w-88 {
  width: 88%;
}

.h-88 {
  height: 88%;
}

.w-880px {
  width: 880px;
}

.h-880px {
  height: 880px;
}

.vw-88 {
  width: 88vw;
}

.vh-88 {
  height: 88vh;
}

.w-89 {
  width: 89%;
}

.h-89 {
  height: 89%;
}

.w-890px {
  width: 890px;
}

.h-890px {
  height: 890px;
}

.vw-89 {
  width: 89vw;
}

.vh-89 {
  height: 89vh;
}

.w-90 {
  width: 90%;
}

.h-90 {
  height: 90%;
}

.w-900px {
  width: 900px;
}

.h-900px {
  height: 900px;
}

.vw-90 {
  width: 90vw;
}

.vh-90 {
  height: 90vh;
}

.w-91 {
  width: 91%;
}

.h-91 {
  height: 91%;
}

.w-910px {
  width: 910px;
}

.h-910px {
  height: 910px;
}

.vw-91 {
  width: 91vw;
}

.vh-91 {
  height: 91vh;
}

.w-92 {
  width: 92%;
}

.h-92 {
  height: 92%;
}

.w-920px {
  width: 920px;
}

.h-920px {
  height: 920px;
}

.vw-92 {
  width: 92vw;
}

.vh-92 {
  height: 92vh;
}

.w-93 {
  width: 93%;
}

.h-93 {
  height: 93%;
}

.w-930px {
  width: 930px;
}

.h-930px {
  height: 930px;
}

.vw-93 {
  width: 93vw;
}

.vh-93 {
  height: 93vh;
}

.w-94 {
  width: 94%;
}

.h-94 {
  height: 94%;
}

.w-940px {
  width: 940px;
}

.h-940px {
  height: 940px;
}

.vw-94 {
  width: 94vw;
}

.vh-94 {
  height: 94vh;
}

.w-95 {
  width: 95%;
}

.h-95 {
  height: 95%;
}

.w-950px {
  width: 950px;
}

.h-950px {
  height: 950px;
}

.vw-95 {
  width: 95vw;
}

.vh-95 {
  height: 95vh;
}

.w-96 {
  width: 96%;
}

.h-96 {
  height: 96%;
}

.w-960px {
  width: 960px;
}

.h-960px {
  height: 960px;
}

.vw-96 {
  width: 96vw;
}

.vh-96 {
  height: 96vh;
}

.w-97 {
  width: 97%;
}

.h-97 {
  height: 97%;
}

.w-970px {
  width: 970px;
}

.h-970px {
  height: 970px;
}

.vw-97 {
  width: 97vw;
}

.vh-97 {
  height: 97vh;
}

.w-98 {
  width: 98%;
}

.h-98 {
  height: 98%;
}

.w-980px {
  width: 980px;
}

.h-980px {
  height: 980px;
}

.vw-98 {
  width: 98vw;
}

.vh-98 {
  height: 98vh;
}

.w-99 {
  width: 99%;
}

.h-99 {
  height: 99%;
}

.w-990px {
  width: 990px;
}

.h-990px {
  height: 990px;
}

.vw-99 {
  width: 99vw;
}

.vh-99 {
  height: 99vh;
}

.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.w-1000px {
  width: 1000px;
}

.h-1000px {
  height: 1000px;
}

.vw-100 {
  width: 100vw;
}

.vh-100 {
  height: 100vh;
}

.hover-bg-primary:hover {
  background: #409EFF;
}

.hover-primary:hover {
  color: #409EFF;
}

.hover-bg-secondary:hover {
  background: #6c757d;
}

.hover-secondary:hover {
  color: #6c757d;
}

.hover-bg-success:hover {
  background: #28a745;
}

.hover-success:hover {
  color: #28a745;
}

.hover-bg-info:hover {
  background: #17a2b8;
}

.hover-info:hover {
  color: #17a2b8;
}

.hover-bg-warning:hover {
  background: #ffc107;
}

.hover-warning:hover {
  color: #ffc107;
}

.hover-bg-danger:hover {
  background: #f56c6c;
}

.hover-danger:hover {
  color: #f56c6c;
}

.hover-bg-light:hover {
  background: #f8f9fa;
}

.hover-light:hover {
  color: #f8f9fa;
}

.hover-bg-dark:hover {
  background: #343a40;
}

.hover-dark:hover {
  color: #343a40;
}

.hover-bg-blue:hover {
  background: #409EFF;
}

.hover-blue:hover {
  color: #409EFF;
}

.hover-bg-indigo:hover {
  background: #6610f2;
}

.hover-indigo:hover {
  color: #6610f2;
}

.hover-bg-purple:hover {
  background: #6f42c1;
}

.hover-purple:hover {
  color: #6f42c1;
}

.hover-bg-pink:hover {
  background: #f9a2c4;
}

.hover-pink:hover {
  color: #f9a2c4;
}

.hover-bg-red:hover {
  background: #f56c6c;
}

.hover-red:hover {
  color: #f56c6c;
}

.hover-bg-orange:hover {
  background: #fd7e14;
}

.hover-orange:hover {
  color: #fd7e14;
}

.hover-bg-yellow:hover {
  background: #ffc107;
}

.hover-yellow:hover {
  color: #ffc107;
}

.hover-bg-green:hover {
  background: #28a745;
}

.hover-green:hover {
  color: #28a745;
}

.hover-bg-teal:hover {
  background: #20c997;
}

.hover-teal:hover {
  color: #20c997;
}

.hover-bg-cyan:hover {
  background: #17a2b8;
}

.hover-cyan:hover {
  color: #17a2b8;
}

.hover-bg-black:hover {
  background: #000;
}

.hover-black:hover {
  color: #000;
}

.hover-bg-white:hover {
  background: #fff;
}

.hover-white:hover {
  color: #fff;
}

.hover-bg-theme-color:hover {
  background: #409EFF;
}

.hover-theme-color:hover {
  color: #409EFF;
}

.hover-bg-gray-100:hover {
  background: #f8f9fa;
}

.hover-gray-100:hover {
  color: #f8f9fa;
}

.hover-bg-gray-200:hover {
  background: #f4f4f4;
}

.hover-gray-200:hover {
  color: #f4f4f4;
}

.hover-bg-gray-300:hover {
  background: #dee2e6;
}

.hover-gray-300:hover {
  color: #dee2e6;
}

.hover-bg-gray-400:hover {
  background: #ced4da;
}

.hover-gray-400:hover {
  color: #ced4da;
}

.hover-bg-gray-500:hover {
  background: #adb5bd;
}

.hover-gray-500:hover {
  color: #adb5bd;
}

.hover-bg-gray-600:hover {
  background: #6c757d;
}

.hover-gray-600:hover {
  color: #6c757d;
}

.hover-bg-gray-700:hover {
  background: #495057;
}

.hover-gray-700:hover {
  color: #495057;
}

.hover-bg-gray-800:hover {
  background: #343a40;
}

.hover-gray-800:hover {
  color: #343a40;
}

.hover-bg-gray-900:hover {
  background: #212529;
}

.hover-gray-900:hover {
  color: #212529;
}

.rotate-5 {
  transform: rotate(45deg);
}

.rotate-10 {
  transform: rotate(45deg);
}

.rotate-15 {
  transform: rotate(45deg);
}

.rotate-20 {
  transform: rotate(45deg);
}

.rotate-25 {
  transform: rotate(45deg);
}

.rotate-30 {
  transform: rotate(45deg);
}

.rotate-35 {
  transform: rotate(45deg);
}

.rotate-40 {
  transform: rotate(45deg);
}

.rotate-45 {
  transform: rotate(45deg);
}

.rotate-50 {
  transform: rotate(45deg);
}

.rotate-55 {
  transform: rotate(45deg);
}

.rotate-60 {
  transform: rotate(45deg);
}

.rotate-65 {
  transform: rotate(45deg);
}

.rotate-70 {
  transform: rotate(45deg);
}

.rotate-75 {
  transform: rotate(45deg);
}

.rotate-80 {
  transform: rotate(45deg);
}

.rotate-85 {
  transform: rotate(45deg);
}

.rotate-90 {
  transform: rotate(45deg);
}

.rotate-95 {
  transform: rotate(45deg);
}

.rotate-100 {
  transform: rotate(45deg);
}

.rotate-105 {
  transform: rotate(45deg);
}

.rotate-110 {
  transform: rotate(45deg);
}

.rotate-115 {
  transform: rotate(45deg);
}

.rotate-120 {
  transform: rotate(45deg);
}

.rotate-125 {
  transform: rotate(45deg);
}

.rotate-130 {
  transform: rotate(45deg);
}

.rotate-135 {
  transform: rotate(45deg);
}

.rotate-140 {
  transform: rotate(45deg);
}

.rotate-145 {
  transform: rotate(45deg);
}

.rotate-150 {
  transform: rotate(45deg);
}

.rotate-155 {
  transform: rotate(45deg);
}

.rotate-160 {
  transform: rotate(45deg);
}

.rotate-165 {
  transform: rotate(45deg);
}

.rotate-170 {
  transform: rotate(45deg);
}

.rotate-175 {
  transform: rotate(45deg);
}

.rotate-180 {
  transform: rotate(45deg);
}

.rotate-185 {
  transform: rotate(45deg);
}

.rotate-190 {
  transform: rotate(45deg);
}

.rotate-195 {
  transform: rotate(45deg);
}

.rotate-200 {
  transform: rotate(45deg);
}

.rotate-205 {
  transform: rotate(45deg);
}

.rotate-210 {
  transform: rotate(45deg);
}

.rotate-215 {
  transform: rotate(45deg);
}

.rotate-220 {
  transform: rotate(45deg);
}

.rotate-225 {
  transform: rotate(45deg);
}

.rotate-230 {
  transform: rotate(45deg);
}

.rotate-235 {
  transform: rotate(45deg);
}

.rotate-240 {
  transform: rotate(45deg);
}

.rotate-245 {
  transform: rotate(45deg);
}

.rotate-250 {
  transform: rotate(45deg);
}

.rotate-255 {
  transform: rotate(45deg);
}

.rotate-260 {
  transform: rotate(45deg);
}

.rotate-265 {
  transform: rotate(45deg);
}

.rotate-270 {
  transform: rotate(45deg);
}

.rotate-275 {
  transform: rotate(45deg);
}

.rotate-280 {
  transform: rotate(45deg);
}

.rotate-285 {
  transform: rotate(45deg);
}

.rotate-290 {
  transform: rotate(45deg);
}

.rotate-295 {
  transform: rotate(45deg);
}

.rotate-300 {
  transform: rotate(45deg);
}

.rotate-305 {
  transform: rotate(45deg);
}

.rotate-310 {
  transform: rotate(45deg);
}

.rotate-315 {
  transform: rotate(45deg);
}

.rotate-320 {
  transform: rotate(45deg);
}

.rotate-325 {
  transform: rotate(45deg);
}

.rotate-330 {
  transform: rotate(45deg);
}

.rotate-335 {
  transform: rotate(45deg);
}

.rotate-340 {
  transform: rotate(45deg);
}

.rotate-345 {
  transform: rotate(45deg);
}

.rotate-350 {
  transform: rotate(45deg);
}

.rotate-355 {
  transform: rotate(45deg);
}

.rotate-360 {
  transform: rotate(45deg);
}

.x-scroll {
  overflow-x: scroll;
}

.y-scroll {
  overflow-y: scroll;
}

.x-auto {
  overflow-x: auto;
}

.y-auto {
  overflow-y: auto;
}

.no-select {
  user-select: none;
}

.scroll-y-0 {
  max-height: 0px;
  overflow-y: auto;
}

.scroll-x-0 {
  max-width: 0px;
  overflow-x: auto;
}

.scroll-y-50 {
  max-height: 50px;
  overflow-y: auto;
}

.scroll-x-50 {
  max-width: 50px;
  overflow-x: auto;
}

.scroll-y-100 {
  max-height: 100px;
  overflow-y: auto;
}

.scroll-x-100 {
  max-width: 100px;
  overflow-x: auto;
}

.scroll-y-150 {
  max-height: 150px;
  overflow-y: auto;
}

.scroll-x-150 {
  max-width: 150px;
  overflow-x: auto;
}

.scroll-y-200 {
  max-height: 200px;
  overflow-y: auto;
}

.scroll-x-200 {
  max-width: 200px;
  overflow-x: auto;
}

.scroll-y-250 {
  max-height: 250px;
  overflow-y: auto;
}

.scroll-x-250 {
  max-width: 250px;
  overflow-x: auto;
}

.scroll-y-300 {
  max-height: 300px;
  overflow-y: auto;
}

.scroll-x-300 {
  max-width: 300px;
  overflow-x: auto;
}

.scroll-y-350 {
  max-height: 350px;
  overflow-y: auto;
}

.scroll-x-350 {
  max-width: 350px;
  overflow-x: auto;
}

.scroll-y-400 {
  max-height: 400px;
  overflow-y: auto;
}

.scroll-x-400 {
  max-width: 400px;
  overflow-x: auto;
}

.scroll-y-450 {
  max-height: 450px;
  overflow-y: auto;
}

.scroll-x-450 {
  max-width: 450px;
  overflow-x: auto;
}

.scroll-y-500 {
  max-height: 500px;
  overflow-y: auto;
}

.scroll-x-500 {
  max-width: 500px;
  overflow-x: auto;
}

.scroll-y-550 {
  max-height: 550px;
  overflow-y: auto;
}

.scroll-x-550 {
  max-width: 550px;
  overflow-x: auto;
}

.scroll-y-600 {
  max-height: 600px;
  overflow-y: auto;
}

.scroll-x-600 {
  max-width: 600px;
  overflow-x: auto;
}

.scroll-y-650 {
  max-height: 650px;
  overflow-y: auto;
}

.scroll-x-650 {
  max-width: 650px;
  overflow-x: auto;
}

.scroll-y-700 {
  max-height: 700px;
  overflow-y: auto;
}

.scroll-x-700 {
  max-width: 700px;
  overflow-x: auto;
}

.scroll-y-750 {
  max-height: 750px;
  overflow-y: auto;
}

.scroll-x-750 {
  max-width: 750px;
  overflow-x: auto;
}

.scroll-y-800 {
  max-height: 800px;
  overflow-y: auto;
}

.scroll-x-800 {
  max-width: 800px;
  overflow-x: auto;
}

.scroll-y-850 {
  max-height: 850px;
  overflow-y: auto;
}

.scroll-x-850 {
  max-width: 850px;
  overflow-x: auto;
}

.scroll-y-900 {
  max-height: 900px;
  overflow-y: auto;
}

.scroll-x-900 {
  max-width: 900px;
  overflow-x: auto;
}

.scroll-y-950 {
  max-height: 950px;
  overflow-y: auto;
}

.scroll-x-950 {
  max-width: 950px;
  overflow-x: auto;
}

.scroll-y-1000 {
  max-height: 1000px;
  overflow-y: auto;
}

.scroll-x-1000 {
  max-width: 1000px;
  overflow-x: auto;
}